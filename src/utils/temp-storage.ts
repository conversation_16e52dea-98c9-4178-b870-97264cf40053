import { supabase } from './supabase';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { TempFileInfo } from '../types/mistral-ocr';

const TEMP_BUCKET = 'temp-ocr-files';
const TEMP_FILE_EXPIRY = 60 * 60 * 1000; // 1 hour in milliseconds

/**
 * Upload a file to temporary storage and get a public URL
 */
export const uploadTempFile = async (
  filePath: string,
  originalFileName?: string
): Promise<TempFileInfo> => {
  try {
    console.log('Uploading temp file to Supabase storage:', filePath);

    // Generate unique filename
    const fileExtension = path.extname(originalFileName || filePath);
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const tempPath = `temp/${Date.now()}/${uniqueFileName}`;

    // Read file
    const fileBuffer = fs.readFileSync(filePath);

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(TEMP_BUCKET)
      .upload(tempPath, fileBuffer, {
        contentType: getContentType(fileExtension),
        cacheControl: '3600', // 1 hour cache
        upsert: false
      });

    if (error) {
      console.error('Error uploading temp file:', error);
      throw new Error(`Failed to upload temp file: ${error.message}`);
    }

    console.log('Temp file uploaded successfully:', data.path);

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(TEMP_BUCKET)
      .getPublicUrl(data.path);

    if (!urlData.publicUrl) {
      throw new Error('Failed to get public URL for temp file');
    }

    console.log('Temp file public URL:', urlData.publicUrl);

    // Return file info with cleanup function
    return {
      path: data.path,
      url: urlData.publicUrl,
      cleanup: async () => {
        try {
          console.log('Cleaning up temp file:', data.path);
          const { error: deleteError } = await supabase.storage
            .from(TEMP_BUCKET)
            .remove([data.path]);
          
          if (deleteError) {
            console.warn('Failed to cleanup temp file:', deleteError);
          } else {
            console.log('Temp file cleaned up successfully');
          }
        } catch (cleanupError) {
          console.warn('Error during temp file cleanup:', cleanupError);
        }
      }
    };

  } catch (error) {
    console.error('Error in uploadTempFile:', error);
    throw error;
  }
};

/**
 * Get content type based on file extension
 */
const getContentType = (extension: string): string => {
  const ext = extension.toLowerCase();
  
  switch (ext) {
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.png':
      return 'image/png';
    case '.bmp':
      return 'image/bmp';
    case '.gif':
      return 'image/gif';
    case '.webp':
      return 'image/webp';
    case '.tiff':
    case '.tif':
      return 'image/tiff';
    case '.heic':
      return 'image/heic';
    case '.heif':
      return 'image/heif';
    case '.pdf':
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
};

/**
 * Clean up expired temp files (utility function for maintenance)
 */
export const cleanupExpiredTempFiles = async (): Promise<void> => {
  try {
    console.log('Starting cleanup of expired temp files...');
    
    const { data: files, error } = await supabase.storage
      .from(TEMP_BUCKET)
      .list('temp', {
        limit: 1000,
        sortBy: { column: 'created_at', order: 'asc' }
      });

    if (error) {
      console.error('Error listing temp files:', error);
      return;
    }

    if (!files || files.length === 0) {
      console.log('No temp files found');
      return;
    }

    const now = Date.now();
    const expiredFiles: string[] = [];

    for (const file of files) {
      if (file.created_at) {
        const fileAge = now - new Date(file.created_at).getTime();
        if (fileAge > TEMP_FILE_EXPIRY) {
          expiredFiles.push(`temp/${file.name}`);
        }
      }
    }

    if (expiredFiles.length > 0) {
      console.log(`Removing ${expiredFiles.length} expired temp files`);
      const { error: deleteError } = await supabase.storage
        .from(TEMP_BUCKET)
        .remove(expiredFiles);

      if (deleteError) {
        console.error('Error removing expired temp files:', deleteError);
      } else {
        console.log('Expired temp files removed successfully');
      }
    } else {
      console.log('No expired temp files found');
    }

  } catch (error) {
    console.error('Error in cleanupExpiredTempFiles:', error);
  }
};

/**
 * Initialize temp storage bucket if it doesn't exist
 */
export const initializeTempStorage = async (): Promise<void> => {
  try {
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('Error listing buckets:', listError);
      return;
    }

    const bucketExists = buckets?.some(bucket => bucket.name === TEMP_BUCKET);
    
    if (!bucketExists) {
      console.log('Creating temp storage bucket...');
      const { error: createError } = await supabase.storage.createBucket(TEMP_BUCKET, {
        public: true,
        allowedMimeTypes: [
          'image/jpeg',
          'image/png',
          'image/bmp',
          'image/gif',
          'image/webp',
          'image/tiff',
          'image/heic',
          'image/heif',
          'application/pdf'
        ],
        fileSizeLimit: 10 * 1024 * 1024 // 10MB
      });

      if (createError) {
        console.error('Error creating temp storage bucket:', createError);
      } else {
        console.log('Temp storage bucket created successfully');
      }
    } else {
      console.log('Temp storage bucket already exists');
    }
  } catch (error) {
    console.error('Error initializing temp storage:', error);
  }
};
